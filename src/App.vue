<!--
  App.vue - Vue应用的根组件
  这是整个Vue应用的入口文件，相当于一个房子的大门
  所有其他的组件都会在这里被引入和使用
-->

<!--
  <script setup> 部分 - JavaScript逻辑区域
  这里写的是组件的"大脑"，负责处理数据和逻辑
  setup是Vue 3的新语法，让代码更简洁易懂
-->
<script setup>
  // 导入Hot组件 - 就像邀请朋友来家里做客一样
  // 从"./views/Hot.vue"这个文件中引入名为Hot的组件
  // 引入后就可以在模板中使用<Hot />标签了
  import Hot from "./views/Hot.vue";
</script>

<!--
  <template> 部分 - HTML模板区域
  这里写的是用户能看到的页面结构，相当于房子的装修布局
  Vue会把这里的代码转换成真正的HTML显示在浏览器中
-->
<template>
  <!--
    Suspense组件 - Vue 3的异步组件包装器
    想象成一个"等待室"，当里面的组件还在加载时，会显示loading提示
    这样用户就不会看到空白页面，体验更好
  -->
  <Suspense>
    <!--
      #default插槽 - 主要内容区域
      当所有组件都加载完成后，这里的内容就会显示出来
      就像等人到齐了才开始聚会一样
    -->
    <template #default>
      <!--
        div容器 - 包装盒
        用来包裹Hot组件，让页面结构更清晰
        就像给礼物包装一个盒子一样
      -->
      <div>
        <!--
          Hot组件 - 热门内容组件
          这是我们从Hot.vue文件中导入的组件
          它会显示热门相关的内容（具体内容由Hot.vue决定）
        -->
        <Hot />
      </div>
    </template>

    <!--
      #fallback插槽 - 加载中的提示内容
      当Hot组件还在加载时（比如网络慢或者组件比较大），
      用户会看到"loading..."这个提示，告诉用户"别着急，正在加载中"
    -->
    <template #fallback>loading...</template>
  </Suspense>
</template>

<!--
  <style> 部分 - CSS样式区域
  这里写的是组件的"化妆"，负责让页面变得好看

  lang="scss" 表示使用SCSS语法（CSS的增强版，功能更强大）
  scoped 表示这里的样式只对当前组件生效，不会影响其他组件
  就像每个房间有自己的装修风格，不会串到别的房间去
-->
<style lang="scss" scoped>
/*
  目前这里是空的，表示使用默认样式
  如果需要自定义样式，可以在这里添加CSS代码
  比如：
  div {
    padding: 20px;  // 内边距
    background-color: #f5f5f5;  // 背景色
  }
*/
</style>
