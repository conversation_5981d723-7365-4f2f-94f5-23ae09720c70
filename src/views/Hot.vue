<script setup>
  import Item from "../components/Item.vue";
  import useHot from "../composables/useHot";
  import Add from "../components/Add.vue";
  import Sort from "../components/Sort.vue";
  const {HotList, load} = useHot();
  load();
</script>

<template>
  <div class="form">
    <Add class="add"/>
    <Sort />
  </div>
  <div class="hot">
    <Item
      class="item"
      v-for="item in HotList"
      :key="item.index"
      :HotData="item" />
  </div>
</template>

<style lang="scss">
.form{
    display: flex;
    margin-bottom: 20px;
    .add{
        flex: 1;
    }
}
  div.hot {
    display: flex;
    flex-direction: column;
    .item {
      margin-bottom: 10px;
    }
  }
</style>
