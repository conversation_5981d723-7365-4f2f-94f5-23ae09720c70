<script setup>
import useHot from '../composables/useHot';
const {orderBy}=useHot()
const order=()=>{
    orderBy.value=orderBy.value=='asc'?'desc':'asc'
}
</script>
<template>
  <div class="sort">
    <button @click="order">
        {{ orderBy=='asc'?'升序':'降序' }}
    </button>
  </div>
</template>
<style lang="scss">
    .sort{
        display: flex;
        button{
            margin-right: 10px;
            background-color: #5575f9;
            color: #fff;
            border: none;
            padding: 5px 10px;
        }
    }
</style>
