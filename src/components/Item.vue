<script setup>
import useHot from '../composables/useHot';
const {del} = useHot();

const {HotData} = defineProps({
    HotData: {
      type: Object,
      required: true,
    },
  });
</script>
<template>
  <section>
    <div class="item">
      <input type="text" :value="HotData.title" />
      <button @click="del(HotData.id)">删除</button>
    </div>
  </section>
</template>
<style lang="scss">
  div.item {
    display: flex;
    input {
      padding: 10px;
      flex: 1;
    }
        button{
            margin-right: 10px;
            background-color: #09fdc8;
            color: #fff;
            border: none;
            padding: 5px 10px;
        }
  }
</style>
