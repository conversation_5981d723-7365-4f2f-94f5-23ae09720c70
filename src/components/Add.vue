<script setup>
import {ref} from "vue";
import useHot from "../composables/useHot";
    const title=ref()
    const onSubmit=()=>{
        useHot().add({title:title.value})
        title.value=''
    }
</script>
<template>
  <div class="add">
    <input type="text" v-model="title" placeholder="回车提交任务" @keyup.enter="onSubmit" />
  </div>
</template>

<style lang="scss">
  div.add {
    display: flex;
    input {
      border: 5px solid #5575f9;
      padding: 10px;
      flex: 1;
    }
  }
</style>
