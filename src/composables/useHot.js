/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-27 19:03:48
 * @LastEditTime: 2025-08-27 20:15:06
 * @FilePath: /day5/src/composables/useHot.js
 */
import useRequest from "./useRequest";
import {ref, watch} from "vue";
const HotList = ref([]);
const orderBy=ref('asc')
export default  () => {
  const request = useRequest();
  const load = async () => {
    HotList.value = await request.get();
    sort()
  };
  const del = async id => {
    await request.del(id);
    load();
  };
  const add=async hot=>{
    await request.post(hot);
    load();
  }
  const sort=()=>{
      HotList.value=Array.prototype.sort.call(HotList.value,(a,b)=>{
        return orderBy.value=='asc'?a.id-b.id:b.id-a.id
    })
  }
  watch(orderBy,()=>sort())
  return {HotList, load, del,add,orderBy};  
  
};
