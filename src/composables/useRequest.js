export default function(){
    return {
        async request(url='',options={method:'get'}){
            return await fetch(`http://127.0.0.1:3000/data/${url}`,options).then(r=>r.json())
        },
        async get(url){
            return await this.request(url)
        },
        async del(url){
            return await this.request(url,{method:'DELETE'})
        },
        async post(data){
            return await this.request('',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(data)})
        }
    }
}